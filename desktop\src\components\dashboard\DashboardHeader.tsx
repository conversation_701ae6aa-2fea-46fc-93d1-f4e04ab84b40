import React, { useState } from 'react'
import {
  Box,
  Typography,
  Chip,
  Avatar,
  IconButton,
  Menu,
  MenuItem,
  <PERSON>vider,
  <PERSON>lt<PERSON>,
  useTheme
} from '@mui/material'
import {
  Wifi,
  WifiOff,
  Cloud,
  CloudOff,
  AccountCircle,
  Logout,
  Store,
  Person
} from '@mui/icons-material'
import { useTranslation } from 'react-i18next'
import { useAuthStore } from '../../store/useAuthStore'
import { useConnectionStatus } from '../../hooks/useConnectionStatus'

export const DashboardHeader: React.FC = () => {
  const { t } = useTranslation()
  const theme = useTheme()
  const { user, logout } = useAuthStore()
  const connectionStatus = useConnectionStatus()

  // User menu state
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null)
  const isMenuOpen = Boolean(anchorEl)

  const handleUserMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget)
  }

  const handleUserMenuClose = () => {
    setAnchorEl(null)
  }

  const handleLogout = async () => {
    handleUserMenuClose()
    await logout()
  }

  const getConnectionIcon = (type: 'internet' | 'backend') => {
    const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend

    if (type === 'internet') {
      return isConnected ? (
        <Wifi sx={{ color: theme.palette.success.main }} />
      ) : (
        <WifiOff sx={{ color: theme.palette.error.main }} />
      )
    } else {
      return isConnected ? (
        <Cloud sx={{ color: theme.palette.success.main }} />
      ) : (
        <CloudOff sx={{ color: theme.palette.error.main }} />
      )
    }
  }

  const getConnectionText = (type: 'internet' | 'backend') => {
    const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend

    if (type === 'internet') {
      return isConnected ? t('header.internet.connected') : t('header.internet.disconnected')
    } else {
      return isConnected ? t('header.backend.connected') : t('header.backend.disconnected')
    }
  }

  const getConnectionColor = (type: 'internet' | 'backend') => {
    const isConnected = type === 'internet' ? connectionStatus.internet : connectionStatus.backend
    return isConnected ? 'success' : 'error'
  }

  // Mock branch name - gerçek projede store'dan gelecek
  const branchName = "Merkez Şube"

  return (
    <Box
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        p: 2,
        backgroundColor: 'background.paper',
        borderRadius: 2,
        boxShadow: 1,
        mb: 3
      }}
    >
      {/* Sol taraf - Bağlantı durumları */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
        {/* İnternet bağlantısı */}
        <Tooltip title={getConnectionText('internet')}>
          <Chip
            icon={getConnectionIcon('internet')}
            label={t('header.internet.label')}
            color={getConnectionColor('internet')}
            variant="outlined"
            size="small"
          />
        </Tooltip>

        {/* Backend bağlantısı */}
        <Tooltip title={getConnectionText('backend')}>
          <Chip
            icon={getConnectionIcon('backend')}
            label={t('header.backend.label')}
            color={getConnectionColor('backend')}
            variant="outlined"
            size="small"
          />
        </Tooltip>
      </Box>

      {/* Orta - Şube adı */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Store sx={{ color: 'text.secondary' }} />
        <Typography variant="h6" color="text.primary" fontWeight="medium">
          {branchName}
        </Typography>
      </Box>

      {/* Sağ taraf - Kullanıcı bilgisi */}
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <Box sx={{ textAlign: 'right', mr: 1 }}>
          <Typography variant="body2" color="text.primary" fontWeight="medium">
            {user?.username || 'Kullanıcı'}
          </Typography>
          <Typography variant="caption" color="text.secondary">
            {user?.role || 'Rol'}
          </Typography>
        </Box>
        
        <Tooltip title={t('header.user.menu')}>
          <IconButton
            onClick={handleUserMenuOpen}
            sx={{
              p: 0.5,
              border: `2px solid ${theme.palette.primary.main}`,
              '&:hover': {
                backgroundColor: 'primary.main',
                '& .MuiAvatar-root': {
                  color: 'white'
                }
              }
            }}
          >
            <Avatar sx={{ width: 32, height: 32, bgcolor: 'transparent' }}>
              <Person sx={{ color: 'primary.main' }} />
            </Avatar>
          </IconButton>
        </Tooltip>

        {/* Kullanıcı menüsü */}
        <Menu
          anchorEl={anchorEl}
          open={isMenuOpen}
          onClose={handleUserMenuClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'right',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'right',
          }}
        >
          <MenuItem disabled>
            <AccountCircle sx={{ mr: 1 }} />
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {user?.username}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {user?.role}
              </Typography>
            </Box>
          </MenuItem>
          
          <Divider />
          
          <MenuItem onClick={handleLogout}>
            <Logout sx={{ mr: 1 }} />
            {t('header.user.logout')}
          </MenuItem>
        </Menu>
      </Box>
    </Box>
  )
}
