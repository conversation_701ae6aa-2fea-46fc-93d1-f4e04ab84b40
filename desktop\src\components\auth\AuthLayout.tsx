import React, { useState, useEffect } from 'react'
import { Box, Container, Paper, Typography, useTheme } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { keyframes } from '@emotion/react'

// Floating animation for background elements
const float = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  33% { transform: translateY(-10px) rotate(1deg); }
  66% { transform: translateY(5px) rotate(-1deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

const floatSlow = keyframes`
  0% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-15px) rotate(2deg); }
  100% { transform: translateY(0px) rotate(0deg); }
`

interface AuthLayoutProps {
  children: React.ReactNode
}

export const AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {
  const theme = useTheme()
  const { t } = useTranslation()

  // Slider state
  const [currentSlide, setCurrentSlide] = useState(0)

  // Slider images - Local assets
  const sliderImages = [
    './assets/images/restaurant-1.jpg',
    './assets/images/restaurant-2.jpg',
    './assets/images/restaurant-3.jpg'
  ]

  // Auto slide effect - Smooth döngü
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentSlide((prev) => {
        const nextSlide = (prev + 1) % sliderImages.length
        return nextSlide
      })
    }, 5000) // 5 saniye - daha uzun süre
    return () => clearInterval(interval)
  }, [sliderImages.length])

  // Preload images - Beyaz flash'ı önle
  useEffect(() => {
    sliderImages.forEach((src) => {
      const img = new Image()
      img.src = src
    })
  }, [])

  return (
    <Box
      sx={{
        height: '100vh',
        width: '100vw',
        display: 'flex',
        backgroundColor: theme.palette.background.default,
        position: 'relative',
        overflow: 'hidden',
        margin: 0,
        padding: 0,
      }}
    >
      {/* Animated Background Elements */}
      <Box
        sx={{
          position: 'absolute',
          top: '10%',
          left: '5%',
          width: 60,
          height: 60,
          borderRadius: '50%',
          backgroundColor: theme.palette.primary.main,
          opacity: 0.1,
          animation: `${float} 6s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          top: '60%',
          right: '10%',
          width: 40,
          height: 40,
          borderRadius: '50%',
          backgroundColor: theme.palette.secondary.main,
          opacity: 0.1,
          animation: `${floatSlow} 8s ease-in-out infinite`,
        }}
      />
      <Box
        sx={{
          position: 'absolute',
          bottom: '20%',
          left: '15%',
          width: 80,
          height: 80,
          borderRadius: '50%',
          backgroundColor: theme.palette.success.main,
          opacity: 0.08,
          animation: `${float} 7s ease-in-out infinite`,
        }}
      />

      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        width: '100%',
        height: '100%',
        p: { xs: 2, sm: 3, md: 4 }
      }}>
        <Paper
          elevation={8}
          sx={{
            display: 'flex',
            width: '100%',
            maxWidth: { xs: '100%', sm: 900, md: 1000 },
            height: { xs: '100%', sm: 'auto' },
            minHeight: { xs: '100%', sm: 600 },
            borderRadius: { xs: 0, sm: 3 },
            overflow: 'hidden',
            boxShadow: { xs: 'none', sm: '0 20px 40px rgba(0,0,0,0.1)' },
            flexDirection: { xs: 'column', md: 'row' }
          }}
        >
          {/* Left Side - Image Slider */}
          <Box
            sx={{
              flex: '0 0 60%', // 60% genişlik - sabit
              position: 'relative',
              minHeight: { xs: 300, sm: 400, md: 600 },
              overflow: 'hidden',
              order: { xs: 2, md: 1 },
              display: { xs: 'none', sm: 'block' }, // Mobile'da gizle
              backgroundColor: '#1a1a1a' // Fallback renk - beyaz flash önleme
            }}
          >
            {/* Image Slider - Layered approach */}
            {sliderImages.map((image, index) => (
              <Box
                key={`slide-${index}`}
                sx={{
                  position: 'absolute',
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  backgroundImage: `url(${image})`,
                  backgroundSize: 'cover',
                  backgroundPosition: 'center center',
                  backgroundRepeat: 'no-repeat',
                  opacity: currentSlide === index ? 1 : 0,
                  filter: currentSlide === index ? 'blur(0px)' : 'blur(2px)',
                  transform: currentSlide === index ? 'scale(1)' : 'scale(1.03)',
                  transition: currentSlide === index
                    ? 'all 1.5s cubic-bezier(0.25, 0.46, 0.45, 0.94)' // Giriş - daha smooth
                    : 'all 0.8s cubic-bezier(0.55, 0.055, 0.675, 0.19)', // Çıkış - daha hızlı
                  zIndex: currentSlide === index ? 2 : 1, // Aktif resim üstte
                  willChange: 'transform, opacity, filter', // Performance
                }}
              />
            ))}



            {/* Slider Indicators - Modern Style */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 30,
                left: '50%',
                transform: 'translateX(-50%)',
                display: 'flex',
                gap: 1.5,
                zIndex: 2,
                backgroundColor: 'rgba(0,0,0,0.3)',
                borderRadius: 3,
                p: 1.5,
                backdropFilter: 'blur(10px)'
              }}
            >
              {sliderImages.map((_, index) => (
                <Box
                  key={index}
                  sx={{
                    width: currentSlide === index ? 24 : 8,
                    height: 8,
                    borderRadius: 4,
                    backgroundColor: currentSlide === index ? '#fff' : 'rgba(255,255,255,0.6)',
                    transition: 'all 0.4s cubic-bezier(0.4, 0, 0.2, 1)',
                    cursor: 'pointer',
                    '&:hover': {
                      backgroundColor: 'rgba(255,255,255,0.8)'
                    }
                  }}
                  onClick={() => setCurrentSlide(index)}
                />
              ))}
            </Box>

            {/* Testimonial - Bottom Positioned */}
            <Box
              sx={{
                position: 'absolute',
                bottom: 30,
                left: 30,
                right: 30,
                zIndex: 3,
                p: 3,
                borderRadius: 2,
                backgroundColor: 'rgba(0,0,0,0.7)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255,255,255,0.2)',
                color: 'white',
              }}
            >
              <Typography
                variant="body1"
                sx={{
                  fontStyle: 'italic',
                  mb: 2,
                  lineHeight: 1.6,
                }}
              >
                "{t('auth.hero.testimonial')}"
              </Typography>
              <Box>
                <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                  {t('auth.hero.author')}
                </Typography>
                <Typography variant="caption" sx={{ opacity: 0.8 }}>
                  {t('auth.hero.position')}
                </Typography>
              </Box>
            </Box>
          </Box>

          {/* Right Side - Form Section */}
          <Box
            sx={{
              flex: '0 0 40%', // 40% genişlik - daha dar
              p: { xs: 3, sm: 4, md: 6 },
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              backgroundColor: theme.palette.background.paper,
              minHeight: { xs: '100%', sm: 400, md: 600 },
              order: { xs: 1, md: 2 },
              width: { xs: '100%', md: 'auto' }
            }}
          >
            {children}
          </Box>
        </Paper>
      </Box>
    </Box>
  )
}
