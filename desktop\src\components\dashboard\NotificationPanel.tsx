import React, { useState } from 'react'
import { 
  Card, 
  CardContent, 
  Typography, 
  Box, 
  List, 
  ListItem, 
  ListItemAvatar, 
  ListItemText,
  Avatar,
  Button,
  Badge,
  Chip,
  IconButton
} from '@mui/material'
import { useTranslation } from 'react-i18next'
import { 
  Notifications,
  Restaurant,
  TableRestaurant,
  Kitchen,
  ShoppingCart,
  MoreVert,
  Circle
} from '@mui/icons-material'

interface Notification {
  id: string
  type: 'order' | 'table' | 'kitchen' | 'general'
  title: string
  message: string
  time: string
  isRead: boolean
  priority: 'low' | 'medium' | 'high'
}

export const NotificationPanel: React.FC = () => {
  const { t } = useTranslation()
  
  // Mock data - gerçek API entegrasyonu sonra yapılacak
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'order',
      title: t('dashboard.notifications.newOrder'),
      message: '<PERSON>sa 5 - Yeni sipariş',
      time: '15:27',
      isRead: false,
      priority: 'high'
    },
    {
      id: '2',
      type: 'kitchen',
      title: t('dashboard.notifications.orderReady'),
      message: 'Sipariş #1234 hazır',
      time: '15:32',
      isRead: false,
      priority: 'medium'
    },
    {
      id: '3',
      type: 'table',
      title: t('dashboard.notifications.tableRequest'),
      message: 'Masa 3 - Hesap talebi',
      time: '15:41',
      isRead: true,
      priority: 'low'
    }
  ])

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'order':
        return <ShoppingCart />
      case 'table':
        return <TableRestaurant />
      case 'kitchen':
        return <Kitchen />
      default:
        return <Restaurant />
    }
  }

  const getNotificationColor = (type: string, priority: string) => {
    if (priority === 'high') return '#f44336'
    if (priority === 'medium') return '#ff9800'
    return '#4caf50'
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'error'
      case 'medium':
        return 'warning'
      default:
        return 'success'
    }
  }

  const unreadCount = notifications.filter(n => !n.isRead).length

  const markAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notification => 
        notification.id === id 
          ? { ...notification, isRead: true }
          : notification
      )
    )
  }

  return (
    <Card sx={{
      height: '100%',
      display: 'flex',
      flexDirection: 'column'
    }}>
      <CardContent sx={{
        flex: 1,
        display: 'flex',
        flexDirection: 'column',
        overflow: 'hidden',
        '&:last-child': { pb: 2 }
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2, flexShrink: 0 }}>
          <Badge badgeContent={unreadCount} color="error">
            <Notifications sx={{ mr: 1 }} />
          </Badge>
          <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
            {t('dashboard.notifications.title')}
          </Typography>
        </Box>

        {notifications.length === 0 ? (
          <Box sx={{ textAlign: 'center', py: 3 }}>
            <Typography variant="body2" color="text.secondary">
              {t('dashboard.notifications.noNotifications')}
            </Typography>
          </Box>
        ) : (
          <Box sx={{
            flex: 1,
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column'
          }}>
            <List sx={{
              p: 0,
              flex: 1,
              overflow: 'auto',
              '&::-webkit-scrollbar': {
                width: '6px',
              },
              '&::-webkit-scrollbar-track': {
                background: 'transparent',
              },
              '&::-webkit-scrollbar-thumb': {
                background: 'rgba(0, 0, 0, 0.2)',
                borderRadius: '3px',
              },
              '&::-webkit-scrollbar-thumb:hover': {
                background: 'rgba(0, 0, 0, 0.3)',
              }
            }}>
            {notifications.map((notification) => (
              <ListItem
                key={notification.id}
                sx={{
                  px: 1,
                  py: 0.5,
                  borderRadius: 1,
                  mb: 0.5,
                  backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                  '&:hover': {
                    backgroundColor: 'action.selected'
                  }
                }}
                secondaryAction={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                    <Typography variant="caption" color="text.secondary">
                      {notification.time}
                    </Typography>
                    {!notification.isRead && (
                      <Circle sx={{ fontSize: 6, color: 'primary.main', ml: 0.5 }} />
                    )}
                  </Box>
                }
              >
                <ListItemAvatar>
                  <Avatar
                    sx={{
                      backgroundColor: getNotificationColor(notification.type, notification.priority),
                      width: 32,
                      height: 32
                    }}
                  >
                    {getNotificationIcon(notification.type)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Typography
                      variant="body2"
                      sx={{
                        fontWeight: notification.isRead ? 'normal' : 'bold',
                        fontSize: '0.8rem',
                        lineHeight: 1.2
                      }}
                    >
                      {notification.title}
                    </Typography>
                  }
                  secondary={
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        fontSize: '0.7rem',
                        lineHeight: 1.1
                      }}
                    >
                      {notification.message}
                    </Typography>
                  }
                />
              </ListItem>
            ))}
          </List>
        )}

        <Button 
          fullWidth 
          variant="contained" 
          color="primary"
          sx={{ mt: 2, borderRadius: 2 }}
          startIcon={<MoreVert />}
        >
          {t('dashboard.notifications.showAll')}
        </Button>
      </CardContent>
    </Card>
  )
}
