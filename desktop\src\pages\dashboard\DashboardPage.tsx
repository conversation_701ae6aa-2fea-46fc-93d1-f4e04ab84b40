import React from 'react'
import { Box, Typo<PERSON>, Grid } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { NotificationPanel, WeatherWidget, ClockWidget, DashboardHeader } from '../../components/dashboard'

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Box sx={{
      minHeight: '100vh',
      width: '100vw',
      p: { xs: 1, sm: 2, md: 3 },
      backgroundColor: 'background.default',
      overflow: 'hidden'
    }}>
      {/* Header */}
      <DashboardHeader />

      <Typography
        variant="h4"
        component="h1"
        gutterBottom
        sx={{
          fontSize: { xs: '1.5rem', sm: '2rem' },
          mb: { xs: 2, sm: 3 }
        }}
      >
        {t('dashboard.title')}
      </Typography>

      <Grid container spacing={{ xs: 2, sm: 3 }} sx={{ height: 'calc(100vh - 200px)' }}>
        {/* Sol Panel - B<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON> */}
        <Grid size={{ xs: 12, md: 5, lg: 4 }}>
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: { xs: 1.5, sm: 2 },
            height: '100%'
          }}>
            {/* Widget'lar Yan Yana */}
            <Box sx={{
              display: 'flex',
              gap: { xs: 1.5, sm: 2 },
              flexDirection: { xs: 'column', sm: 'row' }
            }}>
              <Box sx={{
                flex: 1,
                height: { xs: '80px', sm: '100px' },
                minHeight: '80px'
              }}>
                <ClockWidget />
              </Box>
              <Box sx={{
                flex: 1,
                height: { xs: '80px', sm: '100px' },
                minHeight: '80px'
              }}>
                <WeatherWidget />
              </Box>
            </Box>

            {/* Bildirimler Paneli - Full Width */}
            <Box sx={{ flex: 1, minHeight: 0 }}>
              <NotificationPanel />
            </Box>
          </Box>
        </Grid>

        {/* Sağ Panel - Ana İçerik - Çok Geniş */}
        <Grid size={{ xs: 12, md: 7, lg: 8 }}>
          <Box sx={{
            height: '100%',
            backgroundColor: 'background.paper',
            borderRadius: { xs: 1, sm: 2 },
            p: { xs: 2, sm: 3 },
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: { xs: '300px', md: '100%' }
          }}>
            <Typography
              variant="h6"
              color="text.secondary"
              sx={{
                fontSize: { xs: '1rem', sm: '1.25rem' },
                textAlign: 'center'
              }}
            >
              Ana dashboard içeriği burada geliştirilecek...
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Box>
  )
}
