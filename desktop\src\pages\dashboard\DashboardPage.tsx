import React from 'react'
import { Box, Container, Typography, Grid } from '@mui/material'
import { useTranslation } from 'react-i18next'
import { NotificationPanel, WeatherWidget, ClockWidget } from '../../components/dashboard'

export const DashboardPage: React.FC = () => {
  const { t } = useTranslation()

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        {t('dashboard.title')}
      </Typography>
      
      <Grid container spacing={3}>
        {/* Sol Panel - Bildirimler - <PERSON>ha Dar */}
        <Grid size={{ xs: 12, md: 5, lg: 4 }}>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {/* Widget'lar <PERSON> */}
            <Box sx={{ display: 'flex', gap: 2 }}>
              <Box sx={{ flex: 1, height: '100px' }}>
                <ClockWidget />
              </Box>
              <Box sx={{ flex: 1, height: '100px' }}>
                <WeatherWidget />
              </Box>
            </Box>

            {/* Bildirimler Paneli - Full Width */}
            <NotificationPanel />
          </Box>
        </Grid>

        {/* Sağ Panel - Ana İçerik - Çok Geniş */}
        <Grid size={{ xs: 12, md: 7, lg: 8 }}>
          <Box sx={{
            minHeight: '600px',
            backgroundColor: 'background.paper',
            borderRadius: 2,
            p: 3,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}>
            <Typography variant="h6" color="text.secondary">
              Ana dashboard içeriği burada geliştirilecek...
            </Typography>
          </Box>
        </Grid>
      </Grid>
    </Container>
  )
}
