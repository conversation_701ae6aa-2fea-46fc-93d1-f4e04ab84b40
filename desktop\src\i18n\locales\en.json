{"app": {"title": "Restaurant POS System", "subtitle": "Setup completed successfully!", "testButton": "Test <PERSON>"}, "menu": {"dashboard": "Dashboard", "orders": "Orders", "products": "Products", "categories": "Categories", "tables": "Tables", "customers": "Customers", "reports": "Reports", "settings": "Settings", "users": "Users", "inventory": "Inventory", "kitchen": "Kitchen", "cashier": "Cashier"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome", "notifications": {"title": "Notifications", "newOrder": "New order available!", "orderReady": "Order ready", "tableRequest": "Table request", "kitchenAlert": "Kitchen alert", "showAll": "Show all notifications", "noNotifications": "No new notifications", "markAsRead": "<PERSON> as read"}, "weather": {"title": "Weather", "temperature": "Temperature", "condition": "Condition", "sunny": "<PERSON>", "cloudy": "Cloudy", "rainy": "Rainy", "snowy": "Snowy"}, "clock": {"currentTime": "Current Time", "date": "Date"}}, "header": {"internet": {"label": "Internet", "connected": "Internet connection active", "disconnected": "No internet connection"}, "backend": {"label": "Server", "connected": "Server connection active", "disconnected": "No server connection"}, "user": {"menu": "User menu", "logout": "Logout"}}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "print": "Print", "close": "Close", "open": "Open", "yes": "Yes", "no": "No", "loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "username": "Username", "password": "Password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password"}, "orders": {"newOrder": "New Order", "orderNumber": "Order Number", "table": "Table", "customer": "Customer", "total": "Total", "status": "Status", "date": "Date", "pending": "Pending", "preparing": "Preparing", "ready": "Ready", "delivered": "Delivered", "cancelled": "Cancelled"}, "products": {"name": "Product Name", "price": "Price", "category": "Category", "stock": "Stock", "description": "Description", "image": "Image", "barcode": "Barcode", "unit": "Unit"}, "settings": {"general": "General", "language": "Language", "theme": "Theme", "currency": "<PERSON><PERSON><PERSON><PERSON>", "tax": "Tax", "printer": "Printer", "backup": "Backup"}}