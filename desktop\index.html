<!DOCTYPE html>
<html lang="tr">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <meta name="theme-color" content="#1976d2" />
    <meta name="description" content="Modern Restoran POS Sistemi" />
    <title>Restoran POS</title>

    <!-- Electron için optimize edilmiş meta taglar -->
    <meta http-equiv="Content-Security-Policy" content="default-src 'self' 'unsafe-inline' 'unsafe-eval' data: blob:; connect-src 'self' http://localhost:* ws://localhost:*;" />
    <meta name="format-detection" content="telephone=no" />

    <style>
      /* Yükleme sırasında görünecek stil */
      body {
        margin: 0;
        padding: 0;
        background-color: #f5f5f5;
        overflow: hidden;
      }

      #root {
        height: 100vh;
        width: 100vw;
        overflow: hidden;
      }

      /* Yükleme animasyonu */
      .loading {
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-family: Inter, sans-serif;
        color: #666;
        font-size: 14px;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <div class="loading">Yükleniyor...</div>
    </div>
    <script type="module" src="/src/main.tsx"></script>
  </body>
</html>
